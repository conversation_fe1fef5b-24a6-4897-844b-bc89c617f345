"""
Análisis y Visualización de Resultados
=====================================

Script para analizar los resultados de la simulación de máquinas
y generar gráficos informativos.

Autor: Solución PIA
"""

import matplotlib.pyplot as plt
import numpy as np
from simulacion_maquinas import SimuladorMaquinas, optimizar_asignacion_maquinas

def graficar_costos_vs_maquinas(resultados):
    """Grafica los costos por máquina vs número de máquinas"""
    num_maquinas = [r['num_maquinas'] for r in resultados]
    costos = [r['costo_por_maquina'] for r in resultados]
    errores = [r['costo_std'] for r in resultados]

    plt.figure(figsize=(12, 8))
    plt.errorbar(num_maquinas, costos, yerr=errores, marker='o', capsize=5, capthick=2)
    plt.xlabel('Número de Máquinas por Mecánico')
    plt.ylabel('Costo por Máquina ($)')
    plt.title('Costo por Máquina vs Número de Máquinas por Mecánico')
    plt.grid(True, alpha=0.3)

    # Marcar el punto óptimo
    mejor_idx = np.argmin(costos)
    plt.plot(num_maquinas[mejor_idx], costos[mejor_idx], 'ro', markersize=10,
             label=f'Óptimo: {num_maquinas[mejor_idx]} máquinas')
    plt.legend()

    plt.tight_layout()
    plt.savefig('costos_vs_maquinas.png', dpi=300, bbox_inches='tight')
    plt.show()

def analizar_componentes_costo(num_maquinas_range):
    """Analiza los componentes del costo por separado"""
    costos_ociosidad = []
    costos_mecanicos = []
    costos_totales = []

    for num_maquinas in num_maquinas_range:
        simulador = SimuladorMaquinas(num_maquinas, 1)
        simulador.ejecutar_simulacion()
        costos = simulador.calcular_costos()

        costos_ociosidad.append(costos['costo_ociosidad'])
        costos_mecanicos.append(costos['costo_mecanicos'])
        costos_totales.append(costos['costo_total'])

    plt.figure(figsize=(12, 8))
    plt.plot(num_maquinas_range, costos_ociosidad, 'r-', label='Costo por Ociosidad', linewidth=2)
    plt.plot(num_maquinas_range, costos_mecanicos, 'b-', label='Costo por Salarios', linewidth=2)
    plt.plot(num_maquinas_range, costos_totales, 'g-', label='Costo Total', linewidth=3)

    plt.xlabel('Número de Máquinas por Mecánico')
    plt.ylabel('Costo Anual ($)')
    plt.title('Componentes del Costo vs Número de Máquinas')
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('componentes_costo.png', dpi=300, bbox_inches='tight')
    plt.show()

def analizar_metricas_operativas(num_maquinas_range):
    """Analiza métricas operativas del sistema"""
    tiempos_espera = []
    utilizacion_mecanico = []
    num_reparaciones = []

    for num_maquinas in num_maquinas_range:
        simulador = SimuladorMaquinas(num_maquinas, 1)
        simulador.ejecutar_simulacion()
        costos = simulador.calcular_costos()

        tiempos_espera.append(costos['tiempo_espera_promedio'])
        num_reparaciones.append(costos['num_reparaciones'])

        # Calcular utilización aproximada del mecánico
        tiempo_total_reparacion = costos['num_reparaciones'] * 6  # Tiempo promedio de reparación
        utilizacion = min(100, (tiempo_total_reparacion / simulador.tiempo_simulacion) * 100)
        utilizacion_mecanico.append(utilizacion)

    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 12))

    # Tiempo de espera promedio
    ax1.plot(num_maquinas_range, tiempos_espera, 'r-', linewidth=2)
    ax1.set_ylabel('Tiempo de Espera Promedio (horas)')
    ax1.set_title('Tiempo de Espera vs Número de Máquinas')
    ax1.grid(True, alpha=0.3)

    # Utilización del mecánico
    ax2.plot(num_maquinas_range, utilizacion_mecanico, 'b-', linewidth=2)
    ax2.set_ylabel('Utilización del Mecánico (%)')
    ax2.set_title('Utilización del Mecánico vs Número de Máquinas')
    ax2.grid(True, alpha=0.3)

    # Número de reparaciones
    ax3.plot(num_maquinas_range, num_reparaciones, 'g-', linewidth=2)
    ax3.set_xlabel('Número de Máquinas por Mecánico')
    ax3.set_ylabel('Número de Reparaciones por Año')
    ax3.set_title('Reparaciones Anuales vs Número de Máquinas')
    ax3.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('metricas_operativas.png', dpi=300, bbox_inches='tight')
    plt.show()

def generar_reporte_completo():
    """Genera un reporte completo del análisis"""
    print("Generando reporte completo...")
    print("=" * 80)

    # Ejecutar optimización
    resultados, mejor = optimizar_asignacion_maquinas(max_maquinas=25, num_simulaciones=15)

    # Generar gráficos
    print("\nGenerando gráficos...")
    graficar_costos_vs_maquinas(resultados)

    num_maquinas_range = range(1, 26)
    analizar_componentes_costo(num_maquinas_range)
    analizar_metricas_operativas(num_maquinas_range)

    # Análisis de sensibilidad
    print("\nAnálisis de sensibilidad...")
    print("-" * 40)

    # Probar diferentes escenarios
    escenarios = [
        {"costo_ocioso": 400, "salario": 50, "nombre": "Costo ocioso reducido"},
        {"costo_ocioso": 600, "salario": 50, "nombre": "Costo ocioso aumentado"},
        {"costo_ocioso": 500, "salario": 40, "nombre": "Salario reducido"},
        {"costo_ocioso": 500, "salario": 60, "nombre": "Salario aumentado"}
    ]

    for escenario in escenarios:
        # Modificar temporalmente los costos
        simulador_temp = SimuladorMaquinas(mejor['num_maquinas'], 1)
        simulador_temp.costo_maquina_ociosa = escenario["costo_ocioso"]
        simulador_temp.salario_mecanico = escenario["salario"]

        simulador_temp.ejecutar_simulacion()
        costos_temp = simulador_temp.calcular_costos()

        print(f"{escenario['nombre']}: ${costos_temp['costo_total']:,.2f}")

    print("\n" + "=" * 80)
    print("CONCLUSIONES Y RECOMENDACIONES")
    print("=" * 80)
    print(f"1. Número óptimo de máquinas por mecánico: {mejor['num_maquinas']}")
    print(f"2. Costo total anual estimado: ${mejor['costo_promedio']:,.2f}")
    print("3. Este resultado minimiza la suma de costos de ociosidad y salarios")
    print("4. Se recomienda monitorear el sistema y ajustar según condiciones reales")
    print("5. Considerar factores adicionales como disponibilidad de mecánicos")

    return resultados, mejor

def comparar_estrategias():
    """Compara diferentes estrategias de asignación"""
    print("\nComparación de estrategias de asignación:")
    print("-" * 50)

    estrategias = [
        {"maquinas": 5, "mecanicos": 1, "nombre": "5 máquinas/1 mecánico"},
        {"maquinas": 10, "mecanicos": 1, "nombre": "10 máquinas/1 mecánico"},
        {"maquinas": 15, "mecanicos": 1, "nombre": "15 máquinas/1 mecánico"},
        {"maquinas": 20, "mecanicos": 2, "nombre": "10 máquinas/mecánico (2 mecánicos)"},
        {"maquinas": 30, "mecanicos": 3, "nombre": "10 máquinas/mecánico (3 mecánicos)"}
    ]

    for estrategia in estrategias:
        simulador = SimuladorMaquinas(estrategia["maquinas"], estrategia["mecanicos"])
        simulador.ejecutar_simulacion()
        costos = simulador.calcular_costos()

        costo_por_maquina = costos['costo_total'] / estrategia["maquinas"]

        print(f"{estrategia['nombre']}: ${costos['costo_total']:,.2f} total, "
              f"${costo_por_maquina:.2f} por máquina")

if __name__ == "__main__":
    # Generar reporte completo
    resultados, mejor = generar_reporte_completo()

    # Comparar estrategias
    comparar_estrategias()

    print("\nAnálisis completado. Revisa los gráficos generados en la carpeta PIA.")
