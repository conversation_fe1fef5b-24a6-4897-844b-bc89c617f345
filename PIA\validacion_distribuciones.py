"""
Validación de Distribuciones de Probabilidad
===========================================

Script para validar que las distribuciones generadas coincidan
con las especificaciones del problema.

Autor: Solución PIA
"""

import matplotlib.pyplot as plt
import numpy as np
from simulacion_maquinas import GeneradorTiempos

def validar_distribucion_descompostura(num_muestras=10000):
    """Valida la distribución de tiempo entre descomposturas"""
    generador = GeneradorTiempos()
    muestras = [generador.generar_tiempo_descompostura() for _ in range(num_muestras)]

    # Definir intervalos y probabilidades esperadas
    intervalos = [(6, 8), (8, 10), (10, 12), (12, 14), (16, 18), (18, 20)]
    prob_esperadas = [0.10, 0.15, 0.24, 0.26, 0.18, 0.07]

    # Contar muestras en cada intervalo
    conteos = [0] * len(intervalos)
    for muestra in muestras:
        for i, (min_val, max_val) in enumerate(intervalos):
            if min_val <= muestra < max_val:
                conteos[i] += 1
                break

    # Calcular probabilidades observadas
    prob_observadas = [c / num_muestras for c in conteos]

    print("Validación - Distribución de Tiempo entre Descomposturas")
    print("=" * 60)
    print(f"Número de muestras: {num_muestras:,}")
    print()
    print("Intervalo\t\tEsperada\tObservada\tDiferencia")
    print("-" * 60)

    for i, (intervalo, esp, obs) in enumerate(zip(intervalos, prob_esperadas, prob_observadas)):
        diff = abs(esp - obs)
        print(f"{intervalo[0]}-{intervalo[1]} horas\t\t{esp:.3f}\t\t{obs:.3f}\t\t{diff:.3f}")

    # Crear gráfico
    plt.figure(figsize=(12, 6))
    x = range(len(intervalos))
    width = 0.35

    plt.bar([i - width/2 for i in x], prob_esperadas, width, label='Esperada', alpha=0.8)
    plt.bar([i + width/2 for i in x], prob_observadas, width, label='Observada', alpha=0.8)

    plt.xlabel('Intervalos de Tiempo (horas)')
    plt.ylabel('Probabilidad')
    plt.title('Validación: Distribución de Tiempo entre Descomposturas')
    plt.xticks(x, [f"{i[0]}-{i[1]}" for i in intervalos])
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('validacion_descomposturas.png', dpi=300, bbox_inches='tight')
    plt.show()

    return muestras

def validar_distribucion_reparacion(num_muestras=10000):
    """Valida la distribución de tiempo de reparación"""
    generador = GeneradorTiempos()
    muestras = [generador.generar_tiempo_reparacion() for _ in range(num_muestras)]

    # Definir intervalos y probabilidades esperadas
    intervalos = [(2, 4), (4, 6), (6, 8), (8, 10), (10, 12)]
    prob_esperadas = [0.15, 0.25, 0.30, 0.20, 0.10]

    # Contar muestras en cada intervalo
    conteos = [0] * len(intervalos)
    for muestra in muestras:
        for i, (min_val, max_val) in enumerate(intervalos):
            if min_val <= muestra < max_val:
                conteos[i] += 1
                break

    # Calcular probabilidades observadas
    prob_observadas = [c / num_muestras for c in conteos]

    print("\nValidación - Distribución de Tiempo de Reparación")
    print("=" * 60)
    print(f"Número de muestras: {num_muestras:,}")
    print()
    print("Intervalo\t\tEsperada\tObservada\tDiferencia")
    print("-" * 60)

    for i, (intervalo, esp, obs) in enumerate(zip(intervalos, prob_esperadas, prob_observadas)):
        diff = abs(esp - obs)
        print(f"{intervalo[0]}-{intervalo[1]} horas\t\t{esp:.3f}\t\t{obs:.3f}\t\t{diff:.3f}")

    # Crear gráfico
    plt.figure(figsize=(12, 6))
    x = range(len(intervalos))
    width = 0.35

    plt.bar([i - width/2 for i in x], prob_esperadas, width, label='Esperada', alpha=0.8)
    plt.bar([i + width/2 for i in x], prob_observadas, width, label='Observada', alpha=0.8)

    plt.xlabel('Intervalos de Tiempo (horas)')
    plt.ylabel('Probabilidad')
    plt.title('Validación: Distribución de Tiempo de Reparación')
    plt.xticks(x, [f"{i[0]}-{i[1]}" for i in intervalos])
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('validacion_reparaciones.png', dpi=300, bbox_inches='tight')
    plt.show()

    return muestras

def analizar_estadisticas_descriptivas():
    """Analiza estadísticas descriptivas de las distribuciones"""
    generador = GeneradorTiempos()

    # Generar muestras
    muestras_descompostura = [generador.generar_tiempo_descompostura() for _ in range(10000)]
    muestras_reparacion = [generador.generar_tiempo_reparacion() for _ in range(10000)]

    print("\nEstadísticas Descriptivas")
    print("=" * 40)

    print("\nTiempo entre Descomposturas:")
    print(f"Media: {np.mean(muestras_descompostura):.2f} horas")
    print(f"Mediana: {np.median(muestras_descompostura):.2f} horas")
    print(f"Desviación estándar: {np.std(muestras_descompostura):.2f} horas")
    print(f"Mínimo: {np.min(muestras_descompostura):.2f} horas")
    print(f"Máximo: {np.max(muestras_descompostura):.2f} horas")

    print("\nTiempo de Reparación:")
    print(f"Media: {np.mean(muestras_reparacion):.2f} horas")
    print(f"Mediana: {np.median(muestras_reparacion):.2f} horas")
    print(f"Desviación estándar: {np.std(muestras_reparacion):.2f} horas")
    print(f"Mínimo: {np.min(muestras_reparacion):.2f} horas")
    print(f"Máximo: {np.max(muestras_reparacion):.2f} horas")

    # Crear histogramas
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    ax1.hist(muestras_descompostura, bins=50, alpha=0.7, edgecolor='black')
    ax1.set_xlabel('Tiempo entre Descomposturas (horas)')
    ax1.set_ylabel('Frecuencia')
    ax1.set_title('Histograma: Tiempo entre Descomposturas')
    ax1.grid(True, alpha=0.3)

    ax2.hist(muestras_reparacion, bins=30, alpha=0.7, edgecolor='black', color='orange')
    ax2.set_xlabel('Tiempo de Reparación (horas)')
    ax2.set_ylabel('Frecuencia')
    ax2.set_title('Histograma: Tiempo de Reparación')
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('histogramas_distribuciones.png', dpi=300, bbox_inches='tight')
    plt.show()

def test_chi_cuadrado():
    """Realiza test de chi-cuadrado para validar las distribuciones"""
    from scipy.stats import chisquare

    print("\nTest de Chi-Cuadrado")
    print("=" * 30)

    generador = GeneradorTiempos()
    num_muestras = 50000

    # Test para descomposturas
    muestras_desc = [generador.generar_tiempo_descompostura() for _ in range(num_muestras)]
    intervalos_desc = [(6, 8), (8, 10), (10, 12), (12, 14), (16, 18), (18, 20)]
    prob_esperadas_desc = [0.10, 0.15, 0.24, 0.26, 0.18, 0.07]

    conteos_desc = [0] * len(intervalos_desc)
    for muestra in muestras_desc:
        for i, (min_val, max_val) in enumerate(intervalos_desc):
            if min_val <= muestra < max_val:
                conteos_desc[i] += 1
                break

    frecuencias_esperadas_desc = [p * num_muestras for p in prob_esperadas_desc]
    chi2_desc, p_value_desc = chisquare(conteos_desc, frecuencias_esperadas_desc)

    print(f"Descomposturas - Chi²: {chi2_desc:.4f}, p-value: {p_value_desc:.4f}")

    # Test para reparaciones
    muestras_rep = [generador.generar_tiempo_reparacion() for _ in range(num_muestras)]
    intervalos_rep = [(2, 4), (4, 6), (6, 8), (8, 10), (10, 12)]
    prob_esperadas_rep = [0.15, 0.25, 0.30, 0.20, 0.10]

    conteos_rep = [0] * len(intervalos_rep)
    for muestra in muestras_rep:
        for i, (min_val, max_val) in enumerate(intervalos_rep):
            if min_val <= muestra < max_val:
                conteos_rep[i] += 1
                break

    frecuencias_esperadas_rep = [p * num_muestras for p in prob_esperadas_rep]
    chi2_rep, p_value_rep = chisquare(conteos_rep, frecuencias_esperadas_rep)

    print(f"Reparaciones - Chi²: {chi2_rep:.4f}, p-value: {p_value_rep:.4f}")

    print("\nInterpretación:")
    print("- p-value > 0.05: No se rechaza H0 (distribución es correcta)")
    print("- p-value ≤ 0.05: Se rechaza H0 (distribución puede ser incorrecta)")

if __name__ == "__main__":
    print("Validación de Distribuciones de Probabilidad")
    print("=" * 50)

    # Validar distribuciones
    muestras_desc = validar_distribucion_descompostura()
    muestras_rep = validar_distribucion_reparacion()

    # Analizar estadísticas
    analizar_estadisticas_descriptivas()

    # Test estadístico (requiere scipy)
    try:
        test_chi_cuadrado()
    except ImportError:
        print("\nNota: Para ejecutar el test de chi-cuadrado, instala scipy:")
        print("pip install scipy")

    print("\nValidación completada. Revisa los gráficos generados.")
