"""
Simulación de Mantenimiento de Máquinas
======================================

Problema: Una compañía tiene máquinas que se descomponen según una distribución
de probabilidad conocida. Se necesita determinar cuántas máquinas asignar a
cada mecánico para minimizar costos.

Costos:
- Máquina ociosa: $500/hora
- Salario mecánico: $50/hora

Autor: Solución PIA
"""

import random
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict
import heapq

class GeneradorTiempos:
    """Genera tiempos aleatorios según las distribuciones del problema"""

    def __init__(self):
        # Distribución tiempo entre descomposturas (horas)
        self.intervalos_descompostura = [(6, 8), (8, 10), (10, 12), (12, 14), (16, 18), (18, 20)]
        self.prob_descompostura = [0.10, 0.15, 0.24, 0.26, 0.18, 0.07]

        # Distribución tiempo de reparación (horas)
        self.intervalos_reparacion = [(2, 4), (4, 6), (6, 8), (8, 10), (10, 12)]
        self.prob_reparacion = [0.15, 0.25, 0.30, 0.20, 0.10]

        # Crear distribuciones acumulativas
        self.cum_prob_descompostura = np.cumsum(self.prob_descompostura)
        self.cum_prob_reparacion = np.cumsum(self.prob_reparacion)

    def generar_tiempo_descompostura(self):
        """Genera tiempo hasta la próxima descompostura"""
        r = random.random()
        for i, prob_cum in enumerate(self.cum_prob_descompostura):
            if r <= prob_cum:
                min_tiempo, max_tiempo = self.intervalos_descompostura[i]
                return random.uniform(min_tiempo, max_tiempo)
        return random.uniform(18, 20)  # Fallback

    def generar_tiempo_reparacion(self):
        """Genera tiempo de reparación"""
        r = random.random()
        for i, prob_cum in enumerate(self.cum_prob_reparacion):
            if r <= prob_cum:
                min_tiempo, max_tiempo = self.intervalos_reparacion[i]
                return random.uniform(min_tiempo, max_tiempo)
        return random.uniform(10, 12)  # Fallback

class EventoSimulacion:
    """Representa un evento en la simulación"""

    def __init__(self, tiempo, tipo, maquina_id):
        self.tiempo = tiempo
        self.tipo = tipo  # 'descompostura' o 'fin_reparacion'
        self.maquina_id = maquina_id

    def __lt__(self, other):
        return self.tiempo < other.tiempo

class SimuladorMaquinas:
    """Simula el sistema de máquinas y mecánicos"""

    def __init__(self, num_maquinas, num_mecanicos, tiempo_simulacion=8760):  # 1 año = 8760 horas
        self.num_maquinas = num_maquinas
        self.num_mecanicos = num_mecanicos
        self.tiempo_simulacion = tiempo_simulacion
        self.generador = GeneradorTiempos()

        # Costos
        self.costo_maquina_ociosa = 500  # $/hora
        self.salario_mecanico = 50       # $/hora

        # Estado de la simulación
        self.tiempo_actual = 0
        self.cola_eventos = []
        self.mecanicos_ocupados = 0
        self.cola_reparacion = []

        # Estadísticas
        self.tiempo_total_ocioso = 0
        self.num_reparaciones = 0
        self.tiempos_espera = []

    def inicializar_simulacion(self):
        """Inicializa la simulación programando las primeras descomposturas"""
        self.tiempo_actual = 0
        self.cola_eventos = []
        self.mecanicos_ocupados = 0
        self.cola_reparacion = []
        self.tiempo_total_ocioso = 0
        self.num_reparaciones = 0
        self.tiempos_espera = []

        # Programar primera descompostura para cada máquina
        for maquina_id in range(self.num_maquinas):
            tiempo_descompostura = self.generador.generar_tiempo_descompostura()
            evento = EventoSimulacion(tiempo_descompostura, 'descompostura', maquina_id)
            heapq.heappush(self.cola_eventos, evento)

    def procesar_descompostura(self, evento):
        """Procesa una descompostura de máquina"""
        if self.mecanicos_ocupados < self.num_mecanicos:
            # Hay mecánico disponible
            self.mecanicos_ocupados += 1
            tiempo_reparacion = self.generador.generar_tiempo_reparacion()
            tiempo_fin = self.tiempo_actual + tiempo_reparacion

            evento_fin = EventoSimulacion(tiempo_fin, 'fin_reparacion', evento.maquina_id)
            heapq.heappush(self.cola_eventos, evento_fin)

            self.tiempos_espera.append(0)  # No hubo espera
        else:
            # Máquina debe esperar en cola
            self.cola_reparacion.append((self.tiempo_actual, evento.maquina_id))

    def procesar_fin_reparacion(self, evento):
        """Procesa el fin de una reparación"""
        self.num_reparaciones += 1

        # Programar próxima descompostura de esta máquina
        tiempo_descompostura = self.generador.generar_tiempo_descompostura()
        tiempo_proxima = self.tiempo_actual + tiempo_descompostura

        if tiempo_proxima < self.tiempo_simulacion:
            evento_descompostura = EventoSimulacion(tiempo_proxima, 'descompostura', evento.maquina_id)
            heapq.heappush(self.cola_eventos, evento_descompostura)

        # Verificar si hay máquinas esperando
        if self.cola_reparacion:
            tiempo_inicio_espera, maquina_id = self.cola_reparacion.pop(0)
            tiempo_espera = self.tiempo_actual - tiempo_inicio_espera
            self.tiempos_espera.append(tiempo_espera)
            self.tiempo_total_ocioso += tiempo_espera

            # Iniciar reparación de máquina en espera
            tiempo_reparacion = self.generador.generar_tiempo_reparacion()
            tiempo_fin = self.tiempo_actual + tiempo_reparacion

            evento_fin = EventoSimulacion(tiempo_fin, 'fin_reparacion', maquina_id)
            heapq.heappush(self.cola_eventos, evento_fin)
        else:
            # No hay máquinas esperando, mecánico queda libre
            self.mecanicos_ocupados -= 1

    def ejecutar_simulacion(self):
        """Ejecuta la simulación completa"""
        self.inicializar_simulacion()

        while self.cola_eventos and self.tiempo_actual < self.tiempo_simulacion:
            evento = heapq.heappop(self.cola_eventos)
            self.tiempo_actual = evento.tiempo

            if self.tiempo_actual >= self.tiempo_simulacion:
                break

            if evento.tipo == 'descompostura':
                self.procesar_descompostura(evento)
            elif evento.tipo == 'fin_reparacion':
                self.procesar_fin_reparacion(evento)

        # Agregar tiempo ocioso de máquinas que quedaron en cola al final
        for tiempo_inicio, _ in self.cola_reparacion:
            tiempo_espera = self.tiempo_simulacion - tiempo_inicio
            self.tiempo_total_ocioso += tiempo_espera

    def calcular_costos(self):
        """Calcula los costos totales del sistema"""
        costo_ociosidad = self.tiempo_total_ocioso * self.costo_maquina_ociosa
        costo_mecanicos = self.num_mecanicos * self.salario_mecanico * self.tiempo_simulacion
        costo_total = costo_ociosidad + costo_mecanicos

        return {
            'costo_ociosidad': costo_ociosidad,
            'costo_mecanicos': costo_mecanicos,
            'costo_total': costo_total,
            'tiempo_ocioso_promedio': self.tiempo_total_ocioso / self.num_maquinas if self.num_maquinas > 0 else 0,
            'num_reparaciones': self.num_reparaciones,
            'tiempo_espera_promedio': np.mean(self.tiempos_espera) if self.tiempos_espera else 0
        }

def optimizar_asignacion_maquinas(max_maquinas=50, num_simulaciones=10):
    """
    Encuentra el número óptimo de máquinas por mecánico
    Calcula el costo por máquina para hacer comparaciones justas
    """
    print("Optimizando asignación de máquinas por mecánico...")
    print("=" * 60)

    resultados = []

    # Empezar desde 2 máquinas para que tenga sentido económico
    for num_maquinas in range(2, max_maquinas + 1):
        costos_simulacion = []

        for sim in range(num_simulaciones):
            simulador = SimuladorMaquinas(num_maquinas, 1)  # 1 mecánico
            simulador.ejecutar_simulacion()
            costos = simulador.calcular_costos()
            # Calcular costo por máquina para comparación justa
            costo_por_maquina = costos['costo_total'] / num_maquinas
            costos_simulacion.append(costo_por_maquina)

        costo_promedio = np.mean(costos_simulacion)
        costo_std = np.std(costos_simulacion)
        costo_total_promedio = costo_promedio * num_maquinas

        resultados.append({
            'num_maquinas': num_maquinas,
            'costo_por_maquina': costo_promedio,
            'costo_total': costo_total_promedio,
            'costo_std': costo_std
        })

        # Mostrar progreso para números específicos y los primeros valores
        if num_maquinas <= 10 or num_maquinas % 5 == 0:
            print(f"Máquinas: {num_maquinas:2d} | Costo por máquina: ${costo_promedio:,.2f} | Costo total: ${costo_total_promedio:,.2f}")

    # Encontrar el mínimo costo por máquina
    mejor_resultado = min(resultados, key=lambda x: x['costo_por_maquina'])

    print("\n" + "=" * 60)
    print("RESULTADO ÓPTIMO:")
    print(f"Número óptimo de máquinas por mecánico: {mejor_resultado['num_maquinas']}")
    print(f"Costo por máquina: ${mejor_resultado['costo_por_maquina']:,.2f}")
    print(f"Costo total para {mejor_resultado['num_maquinas']} máquinas: ${mejor_resultado['costo_total']:,.2f}")
    print(f"Desviación estándar: ${mejor_resultado['costo_std']:,.2f}")

    return resultados, mejor_resultado

if __name__ == "__main__":
    # Ejecutar optimización
    resultados, mejor = optimizar_asignacion_maquinas(max_maquinas=20, num_simulaciones=10)

    # Análisis detallado del resultado óptimo
    print("\n" + "=" * 60)
    print("ANÁLISIS DETALLADO DEL RESULTADO ÓPTIMO")
    print("=" * 60)

    simulador_optimo = SimuladorMaquinas(mejor['num_maquinas'], 1)
    simulador_optimo.ejecutar_simulacion()
    costos_detallados = simulador_optimo.calcular_costos()

    print(f"Número de máquinas: {mejor['num_maquinas']}")
    print(f"Número de mecánicos: 1")
    print(f"Costo por ociosidad: ${costos_detallados['costo_ociosidad']:,.2f}")
    print(f"Costo por salarios: ${costos_detallados['costo_mecanicos']:,.2f}")
    print(f"Costo total: ${costos_detallados['costo_total']:,.2f}")
    print(f"Costo por máquina: ${costos_detallados['costo_total'] / mejor['num_maquinas']:,.2f}")
    print(f"Tiempo ocioso promedio por máquina: {costos_detallados['tiempo_ocioso_promedio']:.2f} horas")
    print(f"Número total de reparaciones: {costos_detallados['num_reparaciones']}")
    print(f"Tiempo de espera promedio: {costos_detallados['tiempo_espera_promedio']:.2f} horas")

    # Mostrar comparación con otros números de máquinas
    print(f"\n" + "=" * 60)
    print("COMPARACIÓN CON OTRAS OPCIONES")
    print("=" * 60)

    # Mostrar los 5 mejores resultados
    resultados_ordenados = sorted(resultados, key=lambda x: x['costo_por_maquina'])
    for i, resultado in enumerate(resultados_ordenados[:5]):
        print(f"{i+1}. {resultado['num_maquinas']} máquinas: ${resultado['costo_por_maquina']:,.2f} por máquina")
