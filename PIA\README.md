# Simulación de Mantenimiento de Máquinas

## Descripción del Problema

Una compañía posee un gran número de máquinas que se descomponen según distribuciones de probabilidad conocidas. Se necesita determinar el número óptimo de máquinas que debe asignar a cada mecánico para minimizar los costos totales.

### Datos del Problema

**Distribución de tiempo entre descomposturas:**
| Tiempo (horas) | Probabilidad |
|----------------|--------------|
| 6 - 8          | 0.10         |
| 8 - 10         | 0.15         |
| 10 - 12        | 0.24         |
| 12 - 14        | 0.26         |
| 16 - 18        | 0.18         |
| 18 - 20        | 0.07         |

**Distribución de tiempo de reparación:**
| Tiempo (horas) | Probabilidad |
|----------------|--------------|
| 2 - 4          | 0.15         |
| 4 - 6          | 0.25         |
| 6 - 8          | 0.30         |
| 8 - 10         | 0.20         |
| 10 - 12        | 0.10         |

**Costos:**
- Má<PERSON>a ociosa: $500/hora
- Salario mecánico: $50/hora

## Archivos del Proyecto

### `simulacion_maquinas.py`
Programa principal que contiene:
- **GeneradorTiempos**: Clase para generar tiempos aleatorios según las distribuciones
- **EventoSimulacion**: Clase para representar eventos en la simulación
- **SimuladorMaquinas**: Clase principal que ejecuta la simulación de eventos discretos
- **optimizar_asignacion_maquinas()**: Función que encuentra el número óptimo de máquinas

### `analisis_resultados.py`
Script para análisis y visualización que incluye:
- Gráficos de costos vs número de máquinas
- Análisis de componentes de costo
- Métricas operativas del sistema
- Análisis de sensibilidad
- Comparación de estrategias

## Metodología

### Simulación de Eventos Discretos
El programa utiliza simulación de eventos discretos para modelar:
1. **Descomposturas de máquinas**: Eventos que ocurren según la distribución dada
2. **Reparaciones**: Procesos que toman tiempo según la distribución de reparación
3. **Cola de espera**: Máquinas que esperan cuando todos los mecánicos están ocupados

### Cálculo de Costos
- **Costo de ociosidad**: Tiempo que las máquinas pasan esperando reparación × $500/hora
- **Costo de salarios**: Número de mecánicos × $50/hora × tiempo de simulación
- **Costo total**: Suma de ambos componentes

### Optimización
El programa prueba diferentes números de máquinas por mecánico y encuentra el que minimiza el costo total.

## Uso

### Ejecutar Simulación Básica
```python
python simulacion_maquinas.py
```

### Generar Análisis Completo
```python
python analisis_resultados.py
```

## Resultados Esperados

El programa generará:
1. **Número óptimo de máquinas por mecánico**
2. **Costo total anual estimado**
3. **Gráficos de análisis** (guardados como PNG)
4. **Métricas operativas** (tiempo de espera, utilización, etc.)

## Interpretación de Resultados

### Factores Clave
- **Pocas máquinas por mecánico**: Bajo costo de ociosidad, alto costo de salarios
- **Muchas máquinas por mecánico**: Alto costo de ociosidad, bajo costo de salarios
- **Punto óptimo**: Balance entre ambos costos

### Métricas Importantes
- **Tiempo de espera promedio**: Indica eficiencia del sistema
- **Utilización del mecánico**: Porcentaje de tiempo trabajando
- **Número de reparaciones**: Carga de trabajo anual

## Consideraciones Adicionales

### Supuestos del Modelo
- Las máquinas operan 24/7 durante todo el año
- Los mecánicos están disponibles todo el tiempo
- No hay otros tipos de mantenimiento preventivo
- Los costos son constantes

### Limitaciones
- No considera variabilidad estacional
- No incluye costos de inventario de repuestos
- Asume que todas las reparaciones son exitosas

## Extensiones Posibles

1. **Mantenimiento preventivo**: Incluir mantenimiento programado
2. **Múltiples tipos de máquinas**: Diferentes distribuciones por tipo
3. **Disponibilidad limitada**: Mecánicos con horarios específicos
4. **Costos variables**: Costos que cambian según la hora del día
5. **Optimización multiobjetivo**: Considerar otros factores además del costo

## Dependencias

```python
import random
import numpy as np
import matplotlib.pyplot as plt
import heapq
from collections import defaultdict
```

Para instalar las dependencias:
```bash
pip install numpy matplotlib
```

## Autor
Solución PIA - Proyecto de Simulación de Sistemas
