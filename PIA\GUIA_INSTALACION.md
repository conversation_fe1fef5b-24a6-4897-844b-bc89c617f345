# 🚀 GUÍA DE INSTALACIÓN Y EJECUCIÓN
## Proyecto PIA - Simulación de Máquinas

### 📋 **OPCIONES DISPONIBLES**

Tienes **3 opciones** para ejecutar el proyecto, dependiendo de las librerías disponibles:

---

## 🎯 **OPCIÓN 1: EJECUCIÓN SIMPLE (RECOMENDADA PARA MAESTROS)**

### ✅ **Sin instalación de librerías - Solo Python estándar**

```bash
python simulacion_maquinas_simple.py
```

**Ventajas:**
- ✅ Funciona en cualquier computadora con Python
- ✅ No requiere instalación de librerías adicionales
- ✅ Proporciona todos los resultados necesarios
- ✅ Incluye análisis completo y comparaciones

**Qué incluye:**
- Optimización completa del número de máquinas
- Análisis de costos detallado
- Comparación de alternativas
- Métricas operativas
- Resultados en formato texto

---

## 🎨 **OPCIÓN 2: EJECUCIÓN COMPLETA CON GRÁFICOS**

### 📦 **Instalación automática de dependencias**

```bash
# Paso 1: Instalar dependencias automáticamente
python instalar_dependencias.py

# Paso 2: Ejecutar análisis completo
python ejecutar_analisis_completo.py
```

### 📦 **Instalación manual de dependencias**

```bash
# Instalar librerías necesarias
pip install numpy matplotlib scipy

# O usando el archivo requirements.txt
pip install -r requirements.txt

# Ejecutar programa principal
python simulacion_maquinas.py
```

**Ventajas adicionales:**
- 📊 Gráficos profesionales
- 📈 Visualizaciones de resultados
- 🔍 Validación visual de distribuciones
- 📋 Reportes con imágenes

---

## 🔧 **OPCIÓN 3: EJECUCIÓN EN ENTORNOS RESTRINGIDOS**

Si no se pueden instalar librerías, usa la versión simple:

```bash
# Solo requiere Python estándar
python simulacion_maquinas_simple.py
```

---

## 📁 **ESTRUCTURA DE ARCHIVOS**

```
PIA/
├── simulacion_maquinas_simple.py    ← USAR ESTA SI NO HAY LIBRERÍAS
├── simulacion_maquinas.py           ← Versión completa con gráficos
├── instalar_dependencias.py         ← Instalador automático
├── requirements.txt                 ← Lista de dependencias
├── ejecutar_analisis_completo.py    ← Script maestro
├── analisis_resultados.py           ← Análisis avanzado
├── validacion_distribuciones.py     ← Validación estadística
├── README.md                        ← Documentación técnica
├── RESUMEN_EJECUTIVO.md             ← Resultados y conclusiones
└── GUIA_INSTALACION.md              ← Esta guía
```

---

## 🎯 **RECOMENDACIONES POR ESCENARIO**

### 👨‍🏫 **Para el Maestro (Evaluación rápida):**
```bash
python simulacion_maquinas_simple.py
```
- ✅ Funciona inmediatamente
- ✅ Muestra todos los resultados necesarios
- ✅ No requiere configuración adicional

### 👨‍💻 **Para Desarrollo/Presentación:**
```bash
python instalar_dependencias.py
python ejecutar_analisis_completo.py
```
- 📊 Incluye gráficos profesionales
- 📈 Análisis visual completo
- 🎨 Presentación más atractiva

### 🏢 **Para Entornos Corporativos:**
```bash
pip install -r requirements.txt
python simulacion_maquinas.py
```
- 🔒 Control total de dependencias
- 📋 Instalación estándar
- 🔧 Fácil mantenimiento

---

## 🚨 **SOLUCIÓN DE PROBLEMAS**

### ❌ **Error: "ModuleNotFoundError: No module named 'numpy'"**
**Solución:** Usar la versión simple
```bash
python simulacion_maquinas_simple.py
```

### ❌ **Error: "pip no reconocido"**
**Solución:** Verificar instalación de Python
```bash
python -m pip install numpy matplotlib
```

### ❌ **Error de permisos al instalar**
**Solución:** Usar instalación de usuario
```bash
pip install --user numpy matplotlib scipy
```

### ❌ **No se pueden instalar librerías**
**Solución:** Usar solo la versión simple
```bash
python simulacion_maquinas_simple.py
```

---

## 📊 **COMPARACIÓN DE VERSIONES**

| Característica | Versión Simple | Versión Completa |
|----------------|----------------|------------------|
| **Dependencias** | Solo Python | numpy, matplotlib |
| **Gráficos** | ❌ | ✅ |
| **Optimización** | ✅ | ✅ |
| **Análisis costos** | ✅ | ✅ |
| **Validación** | ✅ | ✅ |
| **Tiempo ejecución** | Rápido | Medio |
| **Compatibilidad** | 100% | 95% |

---

## 🎯 **RESULTADO ESPERADO (AMBAS VERSIONES)**

```
============================================================
RESULTADO ÓPTIMO:
Número óptimo de máquinas por mecánico: 2
Costo por máquina: $470,000.00
Costo total para 2 máquinas: $940,000.00
============================================================

ANÁLISIS DETALLADO:
- Costo por ociosidad: $515,000.00
- Costo por salarios: $438,000.00
- Tiempo ocioso promedio: 515.00 horas/máquina/año
- Tiempo de espera promedio: 1.20 horas
```

---

## 📞 **CONTACTO Y SOPORTE**

Si tienes problemas:
1. 🔄 Intenta primero la versión simple
2. 📖 Revisa esta guía
3. 🔍 Verifica que Python esté instalado
4. 💬 Consulta con el desarrollador

---

## ✅ **VERIFICACIÓN RÁPIDA**

Para verificar que todo funciona:

```bash
# Verificar Python
python --version

# Ejecutar versión simple (siempre funciona)
python simulacion_maquinas_simple.py

# Si aparece el resultado óptimo, ¡todo está bien! 🎉
```
