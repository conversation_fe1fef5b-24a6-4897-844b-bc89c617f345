import math

def leer_entero_positivo(nombre_variable):
    while True:
        try:
            valor = int(input(f"{nombre_variable}: "))
            if valor > 0:
                return valor
            print("→ Debe ser un número mayor que 0.")
        except ValueError:
            print("→ Entrada inválida, intenta nuevamente.")

def obtener_modulo_valido(a, x):
    while True:
        m = leer_entero_positivo("m")
        if m <= max(a, x):
            print(f"→ m debe ser mayor que {max(a, x)}")
            continue

        log2 = math.log(m, 2)
        log10 = math.log(m, 10)

        if log2.is_integer():
            periodo = m // 4
            return m, periodo

        if log10.is_integer():
            if log10 >= 5:
                periodo = int(5 * (10 ** (int(log10) - 2)))
            else:
                periodo = int((5 ** (int(log10) - 1)) * 4)
            return m, periodo

        print("→ m no es potencia entera de 2 ni de 10. Intenta con otro valor.")

def encabezado_tabla():
    print("\n{:>3} | {:>5} | {:^17} | {:>7} | {:>18}".format("n", "X0", "a*X/m", "Xn+1", "Número generado"))
    print("-" * 65)

def ejecutar_generador(a, x0, m, periodo):
    actual = x0
    inicio = x0

    encabezado_tabla()

    for i in range(1, periodo + 1):
        producto = a * actual
        entero = producto // m
        resto = producto % m
        fraccion = f"{entero} + {resto}/{m}"
        numero = round(resto / m, 5)

        print(f"{i:>3} | {actual:>5} | {fraccion:^17} | {resto:>7} | {numero:>18}")

        actual = resto

        if actual == inicio and i != periodo:
            print("\n→ Generador con periodo incompleto.")
            return
        elif actual == inicio and i == periodo:
            print("\n→ Generador con periodo completo.")
        elif i == periodo and actual != inicio:
            print("\n→ Generador con periodo incompleto.")

def main():
    print("Generador Congruencial Multiplicativo")
    print("____________________________________")
    print("Ingresa valores mayores que 0.")
    print("m debe ser mayor que a y X0, y ser potencia de 2 o de 10.\n")

    a = leer_entero_positivo("a")
    x0 = leer_entero_positivo("X0")
    m, periodo = obtener_modulo_valido(a, x0)

    print(f"\nPeriodo esperado: {periodo}")
    ejecutar_generador(a, x0, m, periodo)

    input("\nPresiona Enter para salir...")

if __name__ == "__main__":
    main()
