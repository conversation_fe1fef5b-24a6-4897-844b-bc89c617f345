import random
import heapq

# --- Definición de Distribuciones de Probabilidad ---
# Usaremos los puntos medios de los intervalos

# Tiempo entre descomposturas (horas)
tiempo_descompostura_dist = {
    (6, 8): 0.10,
    (8, 10): 0.15,
    (10, 12): 0.24,
    (12, 14): 0.26,
    (14, 16): 0.0, # Asumimos 0 probabilidad para el hueco 14-16 no especificado
    (16, 18): 0.18,
    (18, 20): 0.07,
}
puntos_medios_descompostura = [ (a + b) / 2 for (a, b) in tiempo_descompostura_dist.keys()]
probabilidades_descompostura = list(tiempo_descompostura_dist.values())

# Tiempo de reparación (horas)
tiempo_reparacion_dist = {
    (2, 4): 0.15,
    (4, 6): 0.25,
    (6, 8): 0.30,
    (8, 10): 0.20,
    (10, 12): 0.10,
}
puntos_medios_reparacion = [ (a + b) / 2 for (a, b) in tiempo_reparacion_dist.keys()]
probabilidades_reparacion = list(tiempo_reparacion_dist.values())

# --- Funciones para Muestrear de las Distribuciones ---
def muestrear_tiempo_descompostura():
    return random.choices(puntos_medios_descompostura, weights=probabilidades_descompostura, k=1)[0]

def muestrear_tiempo_reparacion():
    return random.choices(puntos_medios_reparacion, weights=probabilidades_reparacion, k=1)[0]

# --- Parámetros de la Simulación ---
COSTO_OCIO_POR_HORA = 500
SALARIO_MECANICO_POR_HORA = 50
TIEMPO_SIMULACION = 2000 # Horas (un tiempo suficientemente largo para estabilizar resultados)
NUM_REPLICAS = 10 # Número de veces que se corre la simulación para cada N para promediar

# --- Clase para representar una Máquina ---
class Maquina:
    def __init__(self, id):
        self.id = id
        self.tiempo_hasta_sgte_descompostura = muestrear_tiempo_descompostura()
        self.tiempo_ocio_total = 0
        self.estado = "OPERANDO" # OPERANDO, DESCOMPUESTA, EN_REPARACION
        self.tiempo_descompuesta = 0 # Momento en que se descompuso

# --- Lógica de la Simulación ---
def simular(num_maquinas, tiempo_simulacion_total):
    tiempo_actual = 0
    eventos = [] # (tiempo_evento, tipo_evento, maquina_id)
                 # tipo_evento: "DESCOMPOSTURA", "FIN_REPARACION"

    maquinas = [Maquina(i) for i in range(num_maquinas)]
    for i in range(num_maquinas):
        heapq.heappush(eventos, (maquinas[i].tiempo_hasta_sgte_descompostura, "DESCOMPOSTURA", i))

    mecanico_ocupado_hasta = 0
    cola_reparacion = [] # Almacena (tiempo_descompuesta, maquina_id)

    tiempo_ocio_acumulado_sim = 0

    while tiempo_actual < tiempo_simulacion_total and eventos:
        tiempo_evento, tipo_evento, maquina_id = heapq.heappop(eventos)
        tiempo_transcurrido = tiempo_evento - tiempo_actual
        tiempo_actual = tiempo_evento

        # Actualizar tiempo de ocio de máquinas en cola
        for i_cola in range(len(cola_reparacion)):
            m_id_cola = cola_reparacion[i_cola][1]
            maquinas[m_id_cola].tiempo_ocio_total += tiempo_transcurrido

        if tipo_evento == "DESCOMPOSTURA":
            if maquinas[maquina_id].estado == "OPERANDO": # Solo si estaba operando
                maquinas[maquina_id].estado = "DESCOMPUESTA"
                maquinas[maquina_id].tiempo_descompuesta = tiempo_actual
                # print(f"{tiempo_actual:.2f}: Máquina {maquina_id} se descompuso.")

                if tiempo_actual >= mecanico_ocupado_hasta: # Mecánico libre
                    # print(f"    Mecánico libre. Inicia reparación de máquina {maquina_id}.")
                    tiempo_reparacion = muestrear_tiempo_reparacion()
                    maquinas[maquina_id].estado = "EN_REPARACION"
                    maquinas[maquina_id].tiempo_ocio_total += tiempo_reparacion # Tiempo en reparación es ocio
                    mecanico_ocupado_hasta = tiempo_actual + tiempo_reparacion
                    heapq.heappush(eventos, (mecanico_ocupado_hasta, "FIN_REPARACION", maquina_id))
                else: # Mecánico ocupado
                    # print(f"    Mecánico ocupado hasta {mecanico_ocupado_hasta:.2f}. Máquina {maquina_id} a la cola.")
                    heapq.heappush(cola_reparacion, (maquinas[maquina_id].tiempo_descompuesta, maquina_id))


        elif tipo_evento == "FIN_REPARACION":
            if maquinas[maquina_id].estado == "EN_REPARACION": # Solo si estaba en reparación
                maquinas[maquina_id].estado = "OPERANDO"
                maquinas[maquina_id].tiempo_hasta_sgte_descompostura = muestrear_tiempo_descompostura()
                heapq.heappush(eventos, (tiempo_actual + maquinas[maquina_id].tiempo_hasta_sgte_descompostura, "DESCOMPOSTURA", maquina_id))
                # print(f"{tiempo_actual:.2f}: Máquina {maquina_id} reparada. Próxima falla en {maquinas[maquina_id].tiempo_hasta_sgte_descompostura:.2f} hrs.")

                if cola_reparacion: # Hay máquinas esperando
                    tiempo_descompuesta_cola, maquina_id_cola = heapq.heappop(cola_reparacion)
                    # print(f"    Mecánico toma máquina {maquina_id_cola} de la cola.")
                    tiempo_reparacion = muestrear_tiempo_reparacion()
                    maquinas[maquina_id_cola].estado = "EN_REPARACION"
                    # El tiempo de espera ya se sumó, ahora sumar el tiempo de reparación
                    maquinas[maquina_id_cola].tiempo_ocio_total += tiempo_reparacion
                    mecanico_ocupado_hasta = tiempo_actual + tiempo_reparacion
                    heapq.heappush(eventos, (mecanico_ocupado_hasta, "FIN_REPARACION", maquina_id_cola))
                # else:
                    # print(f"    Mecánico queda libre.")


    # Al final de la simulación, cualquier máquina que no esté operando sigue acumulando ocio
    # y si está en cola, también.
    # Sin embargo, para un tiempo de simulación largo, el impacto de esto se diluye.
    # Una forma más precisa sería manejar el estado final, pero para esta estimación es suficiente.

    for maquina in maquinas:
        if maquina.estado != "OPERANDO" and tiempo_actual < tiempo_simulacion_total :
            # Si la simulación termina y una máquina está rota o en reparación,
            # ese tiempo hasta el fin de la simulación también es ocio.
            # Esta parte es compleja de rastrear perfectamente sin extender eventos más allá del tiempo_simulacion_total
            # Por simplicidad, el tiempo de ocio se cuenta principalmente durante los ciclos completos.
            # El tiempo de ocio ya se va acumulando
            pass
        tiempo_ocio_acumulado_sim += maquina.tiempo_ocio_total

    costo_ocio_total = tiempo_ocio_acumulado_sim * COSTO_OCIO_POR_HORA
    costo_salario_total = SALARIO_MECANICO_POR_HORA * tiempo_simulacion_total # Asumiendo 1 mecánico

    costo_total_simulacion = costo_ocio_total + costo_salario_total
    return costo_total_simulacion, tiempo_ocio_acumulado_sim

# --- Bucle Principal para Encontrar el Número Óptimo de Máquinas ---
print("Simulando para encontrar el número óptimo de máquinas por mecánico...")
print("-----------------------------------------------------------------")
print(f"{'Máquinas':<10} | {'Costo Total Promedio':<25} | {'Tiempo Ocio Promedio (hrs)':<25}")
print("-----------------------------------------------------------------")

mejor_costo_total = float('inf')
optimo_num_maquinas = 0

# Probaremos desde 1 máquina hasta un máximo razonable, e.g., 20 máquinas por mecánico
max_maquinas_a_probar = 20

for n_maquinas in range(1, max_maquinas_a_probar + 1):
    costos_replica = []
    ocios_replica = []
    for _ in range(NUM_REPLICAS):
        costo_total, tiempo_ocio = simular(n_maquinas, TIEMPO_SIMULACION)
        costos_replica.append(costo_total)
        ocios_replica.append(tiempo_ocio)

    costo_promedio = sum(costos_replica) / NUM_REPLICAS
    ocio_promedio = sum(ocios_replica) / NUM_REPLICAS

    print(f"{n_maquinas:<10} | ${costo_promedio:<24.2f} | {ocio_promedio:<25.2f}")

    if costo_promedio < mejor_costo_total:
        mejor_costo_total = costo_promedio
        optimo_num_maquinas = n_maquinas

print("-----------------------------------------------------------------")
print(f"\nResultados de la simulación (para {TIEMPO_SIMULACION} horas y {NUM_REPLICAS} réplicas):")
print(f"El número óptimo de máquinas a asignar a cada mecánico es: {optimo_num_maquinas}")
print(f"Con un costo total promedio estimado de: ${mejor_costo_total:.2f}")