"""
Verificador de Sistema - Proyecto PIA
====================================

Este script verifica qué versión del proyecto puede ejecutarse
en el sistema actual y proporciona recomendaciones.

Uso: python verificar_sistema.py
"""

import sys
import subprocess

def verificar_python():
    """Verifica la versión de Python"""
    version = sys.version_info
    print(f"🐍 Python {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 6:
        print("✅ Versión de Python compatible")
        return True
    else:
        print("❌ Se requiere Python 3.6 o superior")
        return False

def verificar_libreria(nombre):
    """Verifica si una librería está disponible"""
    try:
        __import__(nombre)
        return True
    except ImportError:
        return False

def verificar_pip():
    """Verifica si pip está disponible"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "--version"], 
                            stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        return True
    except:
        return False

def main():
    print("=" * 60)
    print("VERIFICADOR DE SISTEMA - PROYECTO PIA")
    print("=" * 60)
    
    # Verificar Python
    print("\n1. VERIFICANDO PYTHON:")
    print("-" * 30)
    python_ok = verificar_python()
    
    # Verificar librerías
    print("\n2. VERIFICANDO LIBRERÍAS:")
    print("-" * 30)
    
    librerias = {
        'numpy': 'Cálculos numéricos',
        'matplotlib': 'Gráficos',
        'scipy': 'Análisis estadístico (opcional)'
    }
    
    librerias_disponibles = {}
    for lib, descripcion in librerias.items():
        disponible = verificar_libreria(lib)
        librerias_disponibles[lib] = disponible
        status = "✅" if disponible else "❌"
        print(f"{status} {lib:<12} - {descripcion}")
    
    # Verificar pip
    print("\n3. VERIFICANDO PIP:")
    print("-" * 30)
    pip_ok = verificar_pip()
    if pip_ok:
        print("✅ pip está disponible para instalar librerías")
    else:
        print("❌ pip no está disponible")
    
    # Generar recomendaciones
    print("\n" + "=" * 60)
    print("RECOMENDACIONES:")
    print("=" * 60)
    
    if not python_ok:
        print("🚨 CRÍTICO: Actualiza Python a versión 3.6 o superior")
        return
    
    # Determinar qué versión usar
    numpy_ok = librerias_disponibles.get('numpy', False)
    matplotlib_ok = librerias_disponibles.get('matplotlib', False)
    
    if numpy_ok and matplotlib_ok:
        print("🎉 EXCELENTE: Puedes usar la versión completa con gráficos")
        print("\n📋 COMANDOS RECOMENDADOS:")
        print("   python simulacion_maquinas.py")
        print("   python ejecutar_analisis_completo.py")
        
    elif pip_ok:
        print("⚡ BUENO: Puedes instalar las librerías faltantes")
        print("\n📋 COMANDOS RECOMENDADOS:")
        print("   python instalar_dependencias.py")
        print("   # O manualmente:")
        print("   pip install numpy matplotlib scipy")
        
    else:
        print("🔧 BÁSICO: Usa la versión simple sin dependencias")
        print("\n📋 COMANDO RECOMENDADO:")
        print("   python simulacion_maquinas_simple.py")
    
    # Mostrar opciones siempre disponibles
    print("\n" + "=" * 60)
    print("OPCIONES SIEMPRE DISPONIBLES:")
    print("=" * 60)
    print("✅ python simulacion_maquinas_simple.py")
    print("   - Funciona en cualquier sistema con Python")
    print("   - Proporciona todos los resultados necesarios")
    print("   - No requiere librerías adicionales")
    
    # Test rápido
    print("\n" + "=" * 60)
    print("TEST RÁPIDO:")
    print("=" * 60)
    
    respuesta = input("¿Deseas ejecutar un test rápido? (s/n): ").lower()
    
    if respuesta in ['s', 'si', 'sí', 'y', 'yes']:
        print("\nEjecutando test rápido...")
        try:
            # Importar y ejecutar una simulación pequeña
            import random
            import heapq
            
            print("✅ Librerías básicas funcionan correctamente")
            
            # Test simple de la lógica
            from simulacion_maquinas_simple import optimizar_asignacion_maquinas
            print("✅ Lógica de simulación funciona correctamente")
            
            print("\n🎉 ¡SISTEMA VERIFICADO EXITOSAMENTE!")
            print("Puedes ejecutar el proyecto sin problemas.")
            
        except Exception as e:
            print(f"❌ Error en el test: {e}")
            print("Revisa la instalación de Python.")
    
    print("\n" + "=" * 60)
    print("VERIFICACIÓN COMPLETADA")
    print("=" * 60)

if __name__ == "__main__":
    main()
