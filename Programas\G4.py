import numpy as np
import scipy.stats as stats

def kolm<PERSON><PERSON>_smirnov_test(numeros, porcentaje_significancia):
    numeros = np.round(numeros, 5)
    numeros_ordenados = np.sort(numeros)
    n = len(numeros_ordenados)

    d_plus = np.max([(i+1)/n - num for i, num in enumerate(numeros_ordenados)])
    d_minus = np.max([num - i/n for i, num in enumerate(numeros_ordenados)])
    d = max(d_plus, d_minus)

    alpha = porcentaje_significancia / 100

    d_critico = 1.36 / np.sqrt(n) if alpha == 0.05 else stats.kstwo.ppf(1 - alpha, n)

    print(f"Números : {numeros_ordenados}")
    print(f"Estadístico D: {round(d, 5)}")
    print(f"Valor crítico (α = {alpha:.2f}): {round(d_critico, 5)}")

    if d < d_critico:
        print(" Los números son aceptados.")
    else:
        print(" Los números no son aceptados.")

if __name__ == "__main__":
    numeros_rectangulares = [0.12, 0.55, 0.78, 0.31, 0.61, 0.91, 0.47, 0.25, 0.69, 0.37]
    porcentaje = float(input("Ingresa el porcentaje de significancia (ej. 5 para 5%): "))
    
    kolmogorov_smirnov_test(numeros_rectangulares, porcentaje)
