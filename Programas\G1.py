def leer_entero_positivo(nombre_variable):
    while True:
        try:
            valor = int(input(f"{nombre_variable}: "))
            if valor > 0:
                return valor
            print("→ El número debe ser mayor que cero.")
        except ValueError:
            print("→ Entrada inválida, intenta de nuevo.")

def leer_modulo_validado(a, x, c):
    limite = max(a, x, c)
    while True:
        try:
            m = int(input("m: "))
            if m > limite:
                return m
            print(f"→ m debe ser mayor que {limite}.")
        except ValueError:
            print("→ Entrada inválida, intenta de nuevo.")

def mostrar_encabezado():
    print("\n{:^3} | {:^6} | {:^17} | {:^10} | {:^20}".format("n", "X", "(aX+c)/m", "Xn+1", "Número generado"))
    print("-" * 75)

def procesar_generador(a, semilla, c, m):
    actual = semilla
    inicial = semilla
    historial = set()

    mostrar_encabezado()

    for paso in range(1, m + 1):
        if actual in historial:
            print("\n→ Repetición detectada antes del límite.")
            print(f"→ Periodo incompleto: {paso - 1}")
            return
        historial.add(actual)

        operacion = a * actual + c
        siguiente = operacion % m
        mezcla = f"{operacion // m} + {operacion % m}/{m}"
        rnd = round(siguiente / m, 5)

        print("{:^3} | {:^6} | {:^17} | {:^10} | {:^20}".format(paso, actual, mezcla, siguiente, rnd))

        actual = siguiente

    print("\n→ Generador con periodo completo.")
    print(f"→ Periodo total: {m}")

def iniciar():
    print("Generador Congruencial Mixto")
    print("____________________________________")
    print("\nIngresa los siguientes valores:")

    a = leer_entero_positivo("a")
    x0 = leer_entero_positivo("X0")
    c = leer_entero_positivo("c")
    m = leer_modulo_validado(a, x0, c)

    procesar_generador(a, x0, c, m)
    input("\nPresiona Enter para finalizar...")

if __name__ == "__main__":
    iniciar()
