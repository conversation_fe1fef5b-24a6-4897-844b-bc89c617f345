import math
import random


def generar_r_values(n: int) -> list[float]:
    return [random.random() for _ in range(n)]


def poisson_pmf(lmbda: int | float, k: int) -> float:
    return math.exp(-lmbda) * (lmbda ** k) / math.factorial(k)


def construir_tabla_poisson(lmbda: int | float, prob_objetivo: float) -> list[dict]:

    tabla = []
    cdf = 0.0
    k = 0
    while cdf < prob_objetivo:
        p = poisson_pmf(lmbda, k)
        cdf += p
        tabla.append({"k": k, "pmf": p, "cdf": cdf})
        k += 1
    return tabla


def asignar_valores_generados(tabla: list[dict], r_vals: list[float]) -> list[int]:
    """Mapea cada r al menor k cuya CDF sea >= r."""
    resultados = []
    for r in r_vals:
        for fila in tabla:
            if r <= fila["cdf"]:
                resultados.append(fila["k"])
                break
    return resultados


def imprimir_tabla(tabla: list[dict]) -> None:
    print("\n--- Tabla de Distribución de Poisson ---")
    print(f"{'k':^5} | {'P(k)':^12} | {'CDF':^12}")
    print("-" * 37)
    for fila in tabla:
        print(f"{fila['k']:^5} | {fila['pmf']:^12.5f} | {fila['cdf']:^12.5f}")
    print("-" * 37)


def mostrar_resultados(r_vals: list[float], resultados: list[int]) -> None:
    print("\n--- Resultados ---")
    print(f"{'r':^10} | {'k asignado':^12}")
    print("-" * 26)
    for r, k in zip(r_vals, resultados):
        print(f"{r:^10.5f} | {k:^12}")
    print("-" * 26)
    print(f"\nDemanda total: {sum(resultados)}")


def main() -> None:
    try:
        lmbda = int(input("Ingrese el valor de lambda (tambien no de dias a simular): "))
        if lmbda <= 0:
            raise ValueError("lambda debe ser mayor que 0.")
    except ValueError as e:
        print(f"Error: {e}")
        return

    r_vals = generar_r_values(lmbda)
    print("\nValores aleatorios generados (r):", [round(r, 5) for r in r_vals])

    tabla = construir_tabla_poisson(lmbda, max(r_vals))
    resultados = asignar_valores_generados(tabla, r_vals)

    imprimir_tabla(tabla)
    mostrar_resultados(r_vals, resultados)

    input("\nPresiona Enter para salir...")


if __name__ == "__main__":
    main()
