import random
import math
from scipy.stats import norm

def prueba_promedio(n, nivel_confianza):
    numeros = [round(random.random(), 5) for _ in range(n)]
    promedio = sum(numeros) / n

    media_esperada = 0.5
    varianza_esperada = 1/12

    z = (promedio - media_esperada) / math.sqrt(varianza_esperada / n)

    alpha = 1 - nivel_confianza / 100
    z_critico = norm.ppf(1 - alpha / 2)

    print(f"\n--- Resultados ---")
    print(f"Números generados (5 decimales): {numeros}")
    print(f"Promedio de los números generados: {promedio:.5f}")
    print(f"Estadístico Z calculado: {z:.5f}")
    print(f"Z crítico (al {nivel_confianza}%): ±{z_critico:.5f}")

    if abs(z) < z_critico:
        print("Los números son aceptados .")
    else:
        print("Los números no son aceptados .")

try:
    n = int(input("¿Cuántos números aleatorios deseas generar? "))
    nivel_confianza = float(input("¿Qué nivel de confianza deseas usar (porcentaje)? (Ej: 95) "))

    if n <= 0 or not (0 < nivel_confianza < 100):
        raise ValueError("Parámetros inválidos.")

    prueba_promedio(n, nivel_confianza)

except ValueError as e:
    print(" Error en la entrada:", e)
