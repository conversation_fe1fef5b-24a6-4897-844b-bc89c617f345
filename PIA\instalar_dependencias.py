"""
Script de Instalación Automática de Dependencias
===============================================

Este script instala automáticamente todas las librerías necesarias
para ejecutar el proyecto PIA.

Uso: python instalar_dependencias.py
"""

import subprocess
import sys
import os

def instalar_libreria(libreria):
    """Instala una librería usando pip"""
    try:
        print(f"Instalando {libreria}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", libreria])
        print(f"✅ {libreria} instalado correctamente")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ Error instalando {libreria}")
        return False

def verificar_libreria(libreria):
    """Verifica si una librería está instalada"""
    try:
        __import__(libreria)
        return True
    except ImportError:
        return False

def main():
    print("=" * 60)
    print("INSTALADOR DE DEPENDENCIAS - PROYECTO PIA")
    print("=" * 60)
    
    # Lista de librerías necesarias
    librerias = [
        ("numpy", "numpy"),
        ("matplotlib", "matplotlib"),
        ("scipy", "scipy")
    ]
    
    print("Verificando librerías instaladas...")
    print("-" * 40)
    
    necesita_instalacion = []
    
    for nombre_import, nombre_pip in librerias:
        if verificar_libreria(nombre_import):
            print(f"✅ {nombre_import} ya está instalado")
        else:
            print(f"❌ {nombre_import} no está instalado")
            necesita_instalacion.append((nombre_import, nombre_pip))
    
    if not necesita_instalacion:
        print("\n🎉 ¡Todas las librerías ya están instaladas!")
        print("Puedes ejecutar cualquier script del proyecto.")
        return
    
    print(f"\nSe necesita instalar {len(necesita_instalacion)} librería(s):")
    for nombre_import, _ in necesita_instalacion:
        print(f"  - {nombre_import}")
    
    respuesta = input("\n¿Deseas instalar las librerías faltantes? (s/n): ").lower()
    
    if respuesta in ['s', 'si', 'sí', 'y', 'yes']:
        print("\nInstalando librerías...")
        print("-" * 40)
        
        exitos = 0
        for nombre_import, nombre_pip in necesita_instalacion:
            if instalar_libreria(nombre_pip):
                exitos += 1
        
        print(f"\n📊 Resumen de instalación:")
        print(f"  ✅ Exitosas: {exitos}")
        print(f"  ❌ Fallidas: {len(necesita_instalacion) - exitos}")
        
        if exitos == len(necesita_instalacion):
            print("\n🎉 ¡Todas las librerías se instalaron correctamente!")
            print("Ahora puedes ejecutar cualquier script del proyecto.")
        else:
            print("\n⚠️  Algunas instalaciones fallaron.")
            print("Puedes usar 'simulacion_maquinas_simple.py' que no requiere librerías externas.")
    
    else:
        print("\nInstalación cancelada.")
        print("Puedes usar 'simulacion_maquinas_simple.py' que no requiere librerías externas.")
    
    print("\n" + "=" * 60)
    print("OPCIONES PARA EJECUTAR EL PROYECTO:")
    print("=" * 60)
    print("1. CON GRÁFICOS (requiere librerías):")
    print("   python simulacion_maquinas.py")
    print("   python ejecutar_analisis_completo.py")
    print()
    print("2. SIN GRÁFICOS (solo Python estándar):")
    print("   python simulacion_maquinas_simple.py")
    print()
    print("3. INSTALAR MANUALMENTE:")
    print("   pip install numpy matplotlib scipy")

if __name__ == "__main__":
    main()
