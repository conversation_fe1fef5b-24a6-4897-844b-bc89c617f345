#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prueba χ² para parejas (<PERSON> , <PERSON>).  Comprueba uniformidad conjunta en (0,1)²
dividiendo el cuadrado en n × n celdas.
"""

from random import random, seed
from math import floor
from typing import List, Tuple
from scipy.stats import chi2


# ────────────────────────── utilidades ──────────────────────────
def generar_uniformes(cantidad: int, *, semilla: int | None = None) -> List[float]:
    if semilla is not None:
        seed(semilla)
    return [random() for _ in range(cantidad)]


def construir_matriz_frecuencias(pares: List[Tuple[float, float]], k: int) -> List[List[int]]:
    m = [[0] * k for _ in range(k)]
    for x, y in pares:
        col = min(floor(x * k), k - 1)
        fila = min(floor(y * k), k - 1)
        m[fila][col] += 1
    return m


def chi2_2d(matriz: List[List[int]], n_pares: int, k: int) -> float:
    fe = n_pares / (k * k)
    return sum((fo - fe) ** 2 / fe for fila in matriz for fo in fila)


def imprimir_matriz(m: List[List[int]], k: int) -> None:
    ancho = 10
    cab = "y\\x".ljust(8) + "".join(f"[{j/k:.2f},{(j+1)/k:.2f}]".center(ancho) for j in range(k))
    print("\nConteo en celdas X-Y:")
    print(cab)
    print("-" * len(cab))
    for i, fila in enumerate(m):
        etiqueta = f"[{i/k:.2f},{(i+1)/k:.2f}]".ljust(8)
        print(etiqueta + "".join(f"{c:^ {ancho}}" for c in fila))


# ────────────────────────── flujo principal ──────────────────────────
def solicitar_parametros() -> Tuple[int, int, float]:
    while True:
        try:
            N = int(input("Número de pares (N)                : "))
            n = int(input("Número de sub-intervalos (n)        : "))
            α = float(input("Nivel de significancia α (0<α<1)    : "))
            if N <= 0 or n <= 1 or not 0 < α < 1:
                raise ValueError
            return N, n, α
        except ValueError:
            print("✗ Parámetros inválidos, intenta de nuevo.\n")


def main() -> None:
    N, n, α = solicitar_parametros()

    numeros = generar_uniformes(2 * N)              # 2·N números para N pares
    pares = [(numeros[i], numeros[i + 1]) for i in range(0, 2 * N, 2)]

    matriz = construir_matriz_frecuencias(pares, n)
    imprimir_matriz(matriz, n)

    χ2_calc = chi2_2d(matriz, N, n)
    χ2_crit = chi2.ppf(1 - α, n * n - 1)

    print(f"\nχ² calculado : {χ2_calc:.4f}")
    print(f"χ² crítico   : {χ2_crit:.4f}   (gl = {n*n - 1}, α = {α})")
    print("Conclusión   :", end=" ")

    if χ2_calc < χ2_crit:
        print("✓ No se rechaza H₀ → los pares parecen uniformes.")
    else:
        print("✗ Se rechaza H₀ → los pares no parecen uniformes.")

    input("\nPresiona Enter para salir...")


if __name__ == "__main__":
    main()
