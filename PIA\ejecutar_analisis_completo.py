"""
Script Principal para Ejecutar Análisis Completo
===============================================

Este script ejecuta todo el análisis del problema de asignación
de máquinas a mecánicos de manera secuencial.

Autor: Solución PIA
"""

import sys
import os

def ejecutar_analisis_completo():
    """Ejecuta el análisis completo paso a paso"""

    print("=" * 80)
    print("ANÁLISIS COMPLETO: ASIGNACIÓN ÓPTIMA DE MÁQUINAS A MECÁNICOS")
    print("=" * 80)

    print("\n1. VALIDANDO DISTRIBUCIONES DE PROBABILIDAD...")
    print("-" * 50)

    try:
        from validacion_distribuciones import (
            validar_distribucion_descompostura,
            validar_distribucion_reparacion,
            analizar_estadisticas_descriptivas
        )

        # Validar distribuciones con menos muestras para que sea más rápido
        validar_distribucion_descompostura(5000)
        validar_distribucion_reparacion(5000)
        analizar_estadisticas_descriptivas()

        print("✓ Validación de distribuciones completada")

    except Exception as e:
        print(f"⚠ Error en validación de distribuciones: {e}")

    print("\n2. EJECUTANDO OPTIMIZACIÓN PRINCIPAL...")
    print("-" * 50)

    try:
        from simulacion_maquinas import optimizar_asignacion_maquinas

        # Ejecutar optimización con parámetros balanceados
        resultados, mejor = optimizar_asignacion_maquinas(max_maquinas=20, num_simulaciones=10)

        print("✓ Optimización principal completada")

    except Exception as e:
        print(f"⚠ Error en optimización: {e}")
        return

    print("\n3. GENERANDO ANÁLISIS DETALLADO Y GRÁFICOS...")
    print("-" * 50)

    try:
        from analisis_resultados import (
            graficar_costos_vs_maquinas,
            analizar_componentes_costo,
            analizar_metricas_operativas,
            comparar_estrategias
        )

        # Generar gráficos principales
        graficar_costos_vs_maquinas(resultados)

        # Análisis de componentes (rango reducido para velocidad)
        num_maquinas_range = range(1, 21)
        analizar_componentes_costo(num_maquinas_range)
        analizar_metricas_operativas(num_maquinas_range)

        # Comparar estrategias
        comparar_estrategias()

        print("✓ Análisis detallado completado")

    except Exception as e:
        print(f"⚠ Error en análisis detallado: {e}")

    print("\n4. RESUMEN EJECUTIVO")
    print("=" * 50)

    # Mostrar resultados principales
    print(f"RESULTADO ÓPTIMO ENCONTRADO:")
    print(f"• Número de máquinas por mecánico: {mejor['num_maquinas']}")
    print(f"• Costo por máquina: ${mejor['costo_por_maquina']:,.2f}")
    print(f"• Costo total estimado: ${mejor['costo_total']:,.2f}")
    print(f"• Desviación estándar: ${mejor['costo_std']:,.2f}")

    # Análisis adicional del resultado óptimo
    try:
        from simulacion_maquinas import SimuladorMaquinas

        simulador_optimo = SimuladorMaquinas(mejor['num_maquinas'], 1)
        simulador_optimo.ejecutar_simulacion()
        costos_detallados = simulador_optimo.calcular_costos()

        print(f"\nDETALLE DE COSTOS:")
        print(f"• Costo por ociosidad de máquinas: ${costos_detallados['costo_ociosidad']:,.2f}")
        print(f"• Costo por salarios de mecánicos: ${costos_detallados['costo_mecanicos']:,.2f}")
        print(f"• Tiempo ocioso promedio por máquina: {costos_detallados['tiempo_ocioso_promedio']:.1f} horas/año")
        print(f"• Número de reparaciones anuales: {costos_detallados['num_reparaciones']}")
        print(f"• Tiempo de espera promedio: {costos_detallados['tiempo_espera_promedio']:.1f} horas")

    except Exception as e:
        print(f"⚠ Error en análisis detallado del óptimo: {e}")

    print(f"\n5. RECOMENDACIONES")
    print("-" * 30)
    print("• Implementar la asignación óptima encontrada")
    print("• Monitorear el sistema real y comparar con las predicciones")
    print("• Considerar factores adicionales como:")
    print("  - Disponibilidad real de mecánicos")
    print("  - Variaciones estacionales en las descomposturas")
    print("  - Costos de inventario de repuestos")
    print("  - Mantenimiento preventivo")

    print(f"\n6. ARCHIVOS GENERADOS")
    print("-" * 30)
    archivos_esperados = [
        "costos_vs_maquinas.png",
        "componentes_costo.png",
        "metricas_operativas.png",
        "validacion_descomposturas.png",
        "validacion_reparaciones.png",
        "histogramas_distribuciones.png"
    ]

    for archivo in archivos_esperados:
        if os.path.exists(archivo):
            print(f"✓ {archivo}")
        else:
            print(f"⚠ {archivo} (no generado)")

    print("\n" + "=" * 80)
    print("ANÁLISIS COMPLETADO")
    print("=" * 80)
    print("Revisa los gráficos generados en la carpeta PIA para más detalles.")

def mostrar_menu():
    """Muestra un menú de opciones para el usuario"""
    print("\n" + "=" * 60)
    print("MENÚ DE OPCIONES")
    print("=" * 60)
    print("1. Ejecutar análisis completo")
    print("2. Solo optimización rápida")
    print("3. Solo validación de distribuciones")
    print("4. Solo análisis de resultados")
    print("5. Salir")
    print("-" * 60)

def ejecutar_opcion(opcion):
    """Ejecuta la opción seleccionada por el usuario"""

    if opcion == "1":
        ejecutar_analisis_completo()

    elif opcion == "2":
        print("Ejecutando optimización rápida...")
        from simulacion_maquinas import optimizar_asignacion_maquinas
        resultados, mejor = optimizar_asignacion_maquinas(max_maquinas=15, num_simulaciones=5)
        print(f"Resultado: {mejor['num_maquinas']} máquinas por mecánico")
        print(f"Costo: ${mejor['costo_por_maquina']:,.2f} por máquina")

    elif opcion == "3":
        print("Ejecutando validación de distribuciones...")
        from validacion_distribuciones import validar_distribucion_descompostura, validar_distribucion_reparacion
        validar_distribucion_descompostura(3000)
        validar_distribucion_reparacion(3000)

    elif opcion == "4":
        print("Ejecutando análisis de resultados...")
        from simulacion_maquinas import optimizar_asignacion_maquinas
        from analisis_resultados import graficar_costos_vs_maquinas
        resultados, mejor = optimizar_asignacion_maquinas(max_maquinas=15, num_simulaciones=5)
        graficar_costos_vs_maquinas(resultados)

    elif opcion == "5":
        print("¡Hasta luego!")
        return False

    else:
        print("Opción no válida. Intenta de nuevo.")

    return True

if __name__ == "__main__":
    print("Simulación de Asignación de Máquinas a Mecánicos")
    print("Proyecto PIA - Modelado y Simulación")

    if len(sys.argv) > 1 and sys.argv[1] == "--completo":
        # Ejecutar análisis completo directamente
        ejecutar_analisis_completo()
    else:
        # Mostrar menú interactivo
        continuar = True
        while continuar:
            mostrar_menu()
            opcion = input("Selecciona una opción (1-5): ").strip()
            continuar = ejecutar_opcion(opcion)
