import random

def main():
    # Pedir al usuario la cantidad de números a generar
    while True:
        try:
            n = int(input("¿Cuántos números aleatorios deseas generar? "))
            if n <= 0:
                print("Introduce un entero positivo.")
                continue
            break
        except ValueError:
            print("Por favor escribe un número entero válido.")

    # Generar y desplegar los números
    for _ in range(n):
        num = random.random()          # Devuelve un float en el rango [0.0, 1.0)
        print(f"{num:.5f}")            # Formato con 5 decimales

if __name__ == "__main__":
    main()
