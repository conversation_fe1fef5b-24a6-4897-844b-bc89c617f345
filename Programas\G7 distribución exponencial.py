import numpy as np

def generar_numeros_exponenciales(n, lambda_val):
    r_vals = np.round(np.random.uniform(0, 1, n), 5)
    x_vals = np.round(- (1 / lambda_val) * np.log(r_vals), 5)
    return x_vals, r_vals

if __name__ == "__main__":
    cantidad = int(input("¿Cuántos números R deseas generar? "))
    lambda_val = float(input("Ingresa el valor de lambda (> 0): "))

    x_vals, r_vals = generar_numeros_exponenciales(cantidad, lambda_val)
    
    print(f"\nValores R generados: {r_vals}")
    print(f"Valores X generados (distribución exponencial): {x_vals}")
